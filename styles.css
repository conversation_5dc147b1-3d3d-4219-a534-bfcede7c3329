/* CSS Variables */
:root {
    --primary-color: #8b5cf6;
    --secondary-color: #06b6d4;
    --accent-color: #10b981;
    --bg-dark: #0f172a;
    --bg-darker: #020617;
    --bg-card: #1e293b;
    --text-light: #e2e8f0;
    --text-muted: #94a3b8;
    --text-dark: #64748b;
    --border-color: #334155;
    --gradient-primary: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
    --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-dark);
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-darker);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

/* Hide default navigation */
.navbar {
    display: none;
}

/* Left Sidebar - Social Media */
.left-sidebar {
    position: fixed;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: auto;
    height: auto;
    background: transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    gap: 1rem;
}

.left-sidebar .social-icon {
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.left-sidebar .social-icon:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* Right Sidebar - Navigation Menu */
.right-sidebar {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: auto;
    height: auto;
    background: transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    gap: 1.5rem;
}

.right-sidebar .nav-icon {
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    position: relative;
}

.right-sidebar .nav-icon:hover,
.right-sidebar .nav-icon.active {
    color: var(--primary-color);
    transform: scale(1.1);
}

.right-sidebar .nav-icon::before {
    content: attr(data-tooltip);
    position: absolute;
    right: 50px;
    background: var(--bg-card);
    color: var(--text-light);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.right-sidebar .nav-icon:hover::before {
    opacity: 1;
    visibility: visible;
    right: 55px;
}

/* Main Content Area */
.main-content {
    margin-left: 0;
    margin-right: 0;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    background: var(--bg-dark);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-title .name {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-muted);
    margin-bottom: 2rem;
    max-width: 700px;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item-hero {
    text-align: center;
}

.stat-number-hero {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-label-hero {
    font-size: 0.85rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Social Links */
.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.social-link {
    width: 50px;
    height: 50px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    border-color: var(--primary-color);
}

/* Hero Avatar */
.hero-avatar {
    position: relative;
    width: 350px;
    height: 400px;
    margin: 0 auto;
}

.avatar-circle {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    animation: float 6s ease-in-out infinite;
    overflow: hidden;
}

.hero-profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 16px;
}

.avatar-fallback {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: 16px;
}

.avatar-circle::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.3;
    animation: pulse 4s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.1); opacity: 0.1; }
}

/* Hero Tech Stack */
.hero-tech-stack {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 800px;
}

.tech-stack-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: rgba(30, 41, 59, 0.3);
    border: 2px solid var(--primary-color);
    border-radius: 16px;
    backdrop-filter: blur(10px);
}

.tech-icon-item {
    width: 50px;
    height: 50px;
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.tech-icon-item:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
}

/* Section Styling */
.section-padding {
    padding: 6rem 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
}

.section-subtitle {
    color: var(--text-muted);
    text-align: center;
    margin-bottom: 4rem;
    font-size: 1.1rem;
}

/* About Section */
.about-image {
    border-radius: 16px;
    overflow: hidden;
    position: relative;
}

.about-image img {
    width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.about-image:hover img {
    transform: scale(1.05);
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--bg-card);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Services Section */
.service-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    transition: all 0.3s ease;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.2);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto;
}

.service-title {
    color: var(--text-light);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.service-description {
    color: var(--text-muted);
    line-height: 1.6;
}

/* Tech Stack Section */
.tech-stack {
    background: var(--bg-darker);
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.tech-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem 1rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tech-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.tech-item:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.tech-item:hover::before {
    transform: scaleX(1);
}

.tech-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.tech-name {
    font-weight: 600;
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Projects Section */
.project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.project-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.project-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.project-image {
    height: 200px;
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.project-content {
    padding: 1.5rem;
}

.project-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.project-description {
    color: var(--text-muted);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tech-tag {
    background: rgba(139, 92, 246, 0.1);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.project-links {
    display: flex;
    gap: 1rem;
}

.project-link {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.project-link.primary {
    background: var(--gradient-primary);
    color: white;
}

.project-link.secondary {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.project-link:hover {
    transform: translateY(-1px);
}

/* Contact Section */
.contact-section {
    background: var(--bg-darker);
}

.contact-info-card {
    padding: 2rem 0;
}

.contact-icon-box {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-info-title {
    color: var(--text-light);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.contact-info-text {
    color: var(--text-muted);
    margin: 0;
    font-size: 0.95rem;
}

.available-title {
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.available-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.available-list li {
    color: var(--text-muted);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

/* Modern Contact Form */
.contact-form-container {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
}

.contact-form-modern .form-group {
    margin-bottom: 1.5rem;
}

.form-control-modern {
    width: 100%;
    background: var(--bg-dark);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    color: var(--text-light);
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-control-modern:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    background: rgba(30, 41, 59, 0.8);
}

.form-control-modern::placeholder {
    color: var(--text-dark);
}

.btn-send-message {
    background: var(--gradient-primary);
    border: none;
    border-radius: 8px;
    padding: 1rem 2.5rem;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-send-message:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

/* Footer Section */
.footer-section {
    background: var(--bg-dark);
    padding: 2rem 0;
    margin-top: 2rem;
}

.copyright-text {
    color: var(--text-muted);
    margin: 0;
    font-size: 0.9rem;
}



/* Responsive Design */
@media (max-width: 768px) {
    .left-sidebar,
    .right-sidebar {
        display: none;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-avatar {
        width: 280px;
        height: 320px;
        margin-top: 2rem;
    }

    .tech-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 1rem;
    }

    .project-grid {
        grid-template-columns: 1fr;
    }

    .hero-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .stat-number-hero {
        font-size: 1.25rem;
    }

    .stat-label-hero {
        font-size: 0.75rem;
    }

    .hero-tech-stack {
        bottom: 20px;
        max-width: 90%;
    }

    .tech-stack-container {
        gap: 0.8rem;
        padding: 1rem;
        flex-wrap: wrap;
    }

    .tech-icon-item {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    /* Show mobile navigation */
    .navbar {
        display: block !important;
        background-color: rgba(15, 23, 42, 0.95) !important;
        backdrop-filter: blur(10px);
        border-bottom: 1px solid var(--border-color);
    }

    .navbar-brand {
        font-weight: 700;
        font-size: 1.5rem;
        color: var(--text-light) !important;
    }

    .navbar-nav .nav-link {
        color: var(--text-muted) !important;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .navbar-nav .nav-link:hover,
    .navbar-nav .nav-link.active {
        color: var(--primary-color) !important;
    }
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: var(--gradient-primary);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, #8b5cf6, #06b6d4) 1;
}
