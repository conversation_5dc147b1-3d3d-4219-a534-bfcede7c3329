<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Developer Portfolio - Web Developer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <!-- Left Sidebar - Social Media -->
    <div class="left-sidebar">
        <a href="https://github.com/prashant-malla" class="social-icon" title="GitHub" target="_blank">
            <i class="bi bi-github"></i>
        </a>
        <a href="https://www.linkedin.com/in/prashantmalla62" class="social-icon" title="LinkedIn" target="_blank">
            <i class="bi bi-linkedin"></i>
        </a>
        <a href="https://twitter.com/prashantmalla62" class="social-icon" title="Twitter" target="_blank">
            <i class="bi bi-twitter"></i>
        </a>
        <a href="https://www.instagram.com/prashantmalla62/" class="social-icon" title="Instagram" target="_blank">
            <i class="bi bi-instagram"></i>
        </a>
        <a href="mailto:<EMAIL>" class="social-icon" title="Email">
            <i class="bi bi-envelope"></i>
        </a>
    </div>

    <!-- Right Sidebar - Navigation -->
    <div class="right-sidebar">
        <a href="#home" class="nav-icon active" data-tooltip="Home">
            <i class="bi bi-house"></i>
        </a>
        <a href="#about" class="nav-icon" data-tooltip="About">
            <i class="bi bi-person"></i>
        </a>
        <a href="#skills" class="nav-icon" data-tooltip="Skills">
            <i class="bi bi-code-slash"></i>
        </a>
        <a href="#projects" class="nav-icon" data-tooltip="Projects">
            <i class="bi bi-folder"></i>
        </a>
        <a href="#contact" class="nav-icon" data-tooltip="Contact">
            <i class="bi bi-envelope"></i>
        </a>
    </div>

    <!-- Mobile Navigation (hidden on desktop) -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#home">Portfolio</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="#skills">Tech Stack</a></li>
                    <li class="nav-item"><a class="nav-link" href="#projects">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">

    <!-- Hero Section -->
    <section id="home" class="hero-section d-flex align-items-center">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content">
                    <div class="mb-3">
                        <span style="font-size: 1.2rem;">Hi! 👋</span>
                    </div>
                    <h1 class="hero-title">
                        I'm <span class="name">Prashant Malla</span>,
                    </h1>
                    <p class="hero-subtitle">
                        A Web Developer from Nepal, who loves creating websites and digital solutions for businesses and consumers.
                    </p>
                    <div class="hero-buttons">
                        <a href="#contact" class="btn btn-primary">Get In Touch</a>
                        <a href="#" class="btn-outline">📄 RESUME</a>
                    </div>
                    <div class="mt-4">
                        <p class="text-muted mb-3" style="font-size: 0.9rem;">CONNECT WITH ME ON:</p>
                        <div class="social-links d-block d-md-none">
                            <a href="https://github.com/prashant-malla" class="social-link" target="_blank"><i class="bi bi-github"></i></a>
                            <a href="https://www.linkedin.com/in/prashantmalla62" class="social-link" target="_blank"><i class="bi bi-linkedin"></i></a>
                            <a href="https://twitter.com/prashantmalla62" class="social-link" target="_blank"><i class="bi bi-twitter"></i></a>
                            <a href="https://www.facebook.com/prashantmalla62" class="social-link" target="_blank"><i class="bi bi-facebook"></i></a>
                            <a href="https://www.instagram.com/prashantmalla62/" class="social-link" target="_blank"><i class="bi bi-instagram"></i></a>
                            <a href="https://gitlab.com/prashant.malla977" class="social-link" target="_blank"><i class="bi bi-git"></i></a>
                            <a href="mailto:<EMAIL>" class="social-link"><i class="bi bi-envelope"></i></a>
                            <a href="https://www.prashantmalla.com.np/" class="social-link" target="_blank"><i class="bi bi-globe"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="hero-avatar">
                        <div class="avatar-circle">
                            <div style="font-size: 4rem;">👨‍💻</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tech Stack Row -->
            <div class="hero-tech-stack">
                <div class="tech-stack-container">
                    <div class="tech-icon-item">
                        <i class="bi bi-filetype-php"></i>
                    </div>
                    <div class="tech-icon-item">
                        🐍
                    </div>
                    <div class="tech-icon-item">
                        🔥
                    </div>
                    <div class="tech-icon-item">
                        🌿
                    </div>
                    <div class="tech-icon-item">
                        🐳
                    </div>
                    <div class="tech-icon-item">
                        🗄️
                    </div>
                    <div class="tech-icon-item">
                        🐘
                    </div>
                    <div class="tech-icon-item">
                        ⚡
                    </div>
                    <div class="tech-icon-item">
                        ⚛️
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="section-padding">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="about-image">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=400&fit=crop&crop=face" class="img-fluid" alt="About Me">
                    </div>
                </div>
                <div class="col-lg-6">
                    <h2 class="section-title text-start mb-4">About Me</h2>
                    <p class="mb-4" style="color: var(--text-muted);">
                        Hi, I'm Prashant Malla. Nice to meet you. Since beginning my journey as a web developer, I've done remote work for agencies, consulted for startups, and collaborated with talented people to create digital products for both business and consumer use.
                    </p>
                    <p class="mb-4" style="color: var(--text-muted);">
                        I'm quietly confident, naturally curious, and perpetually working on improving my skills. I value simple content structure, clean design patterns, and thoughtful interactions.
                    </p>
                    <div class="about-stats">
                        <div class="stat-item">
                            <span class="stat-number">50+</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">3+</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tech Stack Section -->
    <section id="skills" class="section-padding tech-stack">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">What I do?</h2>
                <p class="section-subtitle">Services and technologies I use to create amazing digital experiences</p>
            </div>

            <!-- Services Grid -->
            <div class="row mb-5">
                <div class="col-lg-4 mb-4">
                    <div class="service-card text-center p-4">
                        <div class="service-icon mb-3">
                            🎨
                        </div>
                        <h4 class="service-title">Web Design</h4>
                        <p class="service-description">I value simple content structure, clean design patterns, and thoughtful interactions.</p>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="service-card text-center p-4">
                        <div class="service-icon mb-3">
                            📈
                        </div>
                        <h4 class="service-title">Digital Marketing</h4>
                        <p class="service-description">Give Us 90 Days, We'll Double Your Organic Traffic</p>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="service-card text-center p-4">
                        <div class="service-icon mb-3">
                            💻
                        </div>
                        <h4 class="service-title">Web Development</h4>
                        <p class="service-description">I genuinely love making websites, and helping fellow developers work on their craft.</p>
                    </div>
                </div>
            </div>

            <div class="text-center mb-4">
                <h3 style="color: var(--text-light); font-size: 1.5rem;">Tech Stack</h3>
            </div>
            <div class="tech-grid">
                <div class="tech-item">
                    <div class="tech-icon">
                        <i class="bi bi-filetype-php"></i>
                    </div>
                    <div class="tech-name">PHP</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🐍
                    </div>
                    <div class="tech-name">Python</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🔥
                    </div>
                    <div class="tech-name">Laravel</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🌿
                    </div>
                    <div class="tech-name">Django</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🐳
                    </div>
                    <div class="tech-name">Docker</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🗄️
                    </div>
                    <div class="tech-name">MySQL</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🐘
                    </div>
                    <div class="tech-name">PostgreSQL</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        ⚡
                    </div>
                    <div class="tech-name">JavaScript</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        ⚛️
                    </div>
                    <div class="tech-name">React</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🌐
                    </div>
                    <div class="tech-name">HTML5</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🎨
                    </div>
                    <div class="tech-name">CSS3</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        📦
                    </div>
                    <div class="tech-name">Git</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recent Works Section -->
    <section id="projects" class="section-padding">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">My Works</h2>
                <p class="section-subtitle">Here are some of my work</p>
            </div>

            <!-- Featured Project -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="project-card">
                        <div class="row g-0">
                            <div class="col-md-6">
                                <div class="project-image" style="height: 300px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                                    🚀 Featured Project
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="project-content" style="height: 300px; display: flex; flex-direction: column; justify-content: center;">
                                    <h3 class="project-title">Website For School</h3>
                                    <p class="project-description">
                                        This is a website of Shishu Jyoti English Boarding School was established in 2054 B.S by Founder Principal Shiv Shankar Mahato. A complete school management system with student portal and admin dashboard.
                                    </p>
                                    <div class="project-tech">
                                        <span class="tech-tag">PHP</span>
                                        <span class="tech-tag">Laravel</span>
                                        <span class="tech-tag">MySQL</span>
                                        <span class="tech-tag">Bootstrap</span>
                                    </div>
                                    <div class="project-links">
                                        <a href="https://shishujyoti.edu.np/" class="project-link primary" target="_blank">Visit Site</a>
                                        <a href="#" class="project-link secondary">View Details</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Grid -->
            <div class="project-grid">
                <div class="project-card">
                    <div class="project-image" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                        🏢 IT Company
                    </div>
                    <div class="project-content">
                        <h4 class="project-title">Website For IT Company</h4>
                        <p class="project-description">
                            This is a website for an IT company that provides IT solutions to their clients. Modern design with service showcase and client portfolio.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">PHP</span>
                            <span class="tech-tag">Laravel</span>
                            <span class="tech-tag">MySQL</span>
                        </div>
                        <div class="project-links">
                            <a href="https://iesmac.com/" class="project-link primary" target="_blank">Visit Site</a>
                            <a href="#" class="project-link secondary">Details</a>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                        📝 Blog Platform
                    </div>
                    <div class="project-content">
                        <h4 class="project-title">Personal Blog & Portfolio</h4>
                        <p class="project-description">
                            A complete blog platform with content management, categories, and responsive design. Features include article publishing and SEO optimization.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">PHP</span>
                            <span class="tech-tag">MySQL</span>
                            <span class="tech-tag">Bootstrap</span>
                        </div>
                        <div class="project-links">
                            <a href="https://www.prashantmalla.com.np/" class="project-link primary" target="_blank">Visit Site</a>
                            <a href="#" class="project-link secondary">Details</a>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                        🛠️ Custom Solutions
                    </div>
                    <div class="project-content">
                        <h4 class="project-title">Custom Web Applications</h4>
                        <p class="project-description">
                            Various custom web applications built for startups and agencies, including CRM systems, inventory management, and business automation tools.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">Laravel</span>
                            <span class="tech-tag">Vue.js</span>
                            <span class="tech-tag">API Integration</span>
                        </div>
                        <div class="project-links">
                            <a href="#contact" class="project-link primary">Discuss Project</a>
                            <a href="#" class="project-link secondary">Portfolio</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section-padding contact-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Drop us a line</h2>
                <p class="section-subtitle" style="max-width: 600px; margin: 0 auto;">
                    I'm seeking out opportunities to collaborate with companies, agencies, individuals, not just work for them. I want to bring my collective development experience to the table where we can work together to solve real business-problems in a way that optimizes all of our respective experience and knowledge.
                </p>
            </div>

            <!-- Contact Info -->
            <div class="row mb-5">
                <div class="col-lg-4 text-center mb-4">
                    <div class="contact-info-item">
                        <div class="contact-icon mb-3">📍</div>
                        <h5 style="color: var(--text-light);">Location</h5>
                        <p style="color: var(--text-muted);">Godawari 01, Lalitpur, Nepal</p>
                    </div>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <div class="contact-info-item">
                        <div class="contact-icon mb-3">📞</div>
                        <h5 style="color: var(--text-light);">Phone</h5>
                        <p style="color: var(--text-muted);">(+977) 9843</p>
                    </div>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <div class="contact-info-item">
                        <div class="contact-icon mb-3">📧</div>
                        <h5 style="color: var(--text-light);">Email</h5>
                        <p style="color: var(--text-muted);"><EMAIL></p>
                    </div>
                </div>
            </div>

            <div class="text-center mb-5">
                <h3 style="color: var(--text-light); font-size: 1.5rem; margin-bottom: 2rem;">Let's Connect</h3>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <form class="contact-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="email" class="form-control" placeholder="Email" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <textarea class="form-control" placeholder="Message" rows="6" required></textarea>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary" style="padding: 1rem 3rem;">Send Message</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>


    </div> <!-- End main-content -->

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
        crossorigin="anonymous"></script>
    
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Update active navigation link on scroll
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link, .right-sidebar .nav-icon');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (scrollY >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });

        // Add click handlers for right sidebar navigation
        document.querySelectorAll('.right-sidebar .nav-icon').forEach(icon => {
            icon.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all icons
                document.querySelectorAll('.right-sidebar .nav-icon').forEach(i => {
                    i.classList.remove('active');
                });

                // Add active class to clicked icon
                this.classList.add('active');

                // Smooth scroll to target
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>

</html>
