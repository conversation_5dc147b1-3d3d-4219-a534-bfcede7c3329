<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Developer Portfolio - Web Developer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#home">Portfolio</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="#skills">Tech Stack</a></li>
                    <li class="nav-item"><a class="nav-link" href="#projects">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section d-flex align-items-center">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content">
                    <div class="mb-3">
                        <span style="font-size: 1.2rem;">Hi! 👋</span>
                    </div>
                    <h1 class="hero-title">
                        I'm <span class="name">Alex Johnson</span>,
                    </h1>
                    <p class="hero-subtitle">
                        A Full-Stack Developer & DevOps Engineer, who loves intuitive, clean and modern technologies.
                    </p>
                    <div class="hero-buttons">
                        <a href="#contact" class="btn btn-primary">Get In Touch</a>
                        <a href="#" class="btn-outline">📄 RESUME</a>
                    </div>
                    <div class="mt-4">
                        <p class="text-muted mb-3" style="font-size: 0.9rem;">CONNECT WITH ME ON:</p>
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="bi bi-github"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-linkedin"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-twitter"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-discord"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-instagram"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-youtube"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-envelope"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-globe"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="hero-avatar">
                        <div class="avatar-circle">
                            <div style="font-size: 4rem;">👨‍💻</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="section-padding">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="about-image">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=400&fit=crop&crop=face" class="img-fluid" alt="About Me">
                    </div>
                </div>
                <div class="col-lg-6">
                    <h2 class="section-title text-start mb-4">About Me</h2>
                    <p class="mb-4" style="color: var(--text-muted);">
                        I'm a dedicated Full-Stack Developer & DevOps Engineer with 5+ years of experience in creating digital solutions that make a difference. I specialize in modern web technologies and love turning complex problems into simple, beautiful designs.
                    </p>
                    <p class="mb-4" style="color: var(--text-muted);">
                        When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or enjoying a good cup of coffee while reading about the latest development trends.
                    </p>
                    <div class="about-stats">
                        <div class="stat-item">
                            <span class="stat-number">100+</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">5+</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tech Stack Section -->
    <section id="skills" class="section-padding tech-stack">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Tech Stack</h2>
                <p class="section-subtitle">Technologies and tools I use to build scalable, efficient web solutions</p>
            </div>
            <div class="tech-grid">
                <div class="tech-item">
                    <div class="tech-icon">
                        <i class="bi bi-filetype-php"></i>
                    </div>
                    <div class="tech-name">PHP</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🐍
                    </div>
                    <div class="tech-name">Python</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🔥
                    </div>
                    <div class="tech-name">Laravel</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🌿
                    </div>
                    <div class="tech-name">Django</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🐳
                    </div>
                    <div class="tech-name">Docker</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🗄️
                    </div>
                    <div class="tech-name">MySQL</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🐘
                    </div>
                    <div class="tech-name">PostgreSQL</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        ⚡
                    </div>
                    <div class="tech-name">JavaScript</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        ⚛️
                    </div>
                    <div class="tech-name">React</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🌐
                    </div>
                    <div class="tech-name">HTML5</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        🎨
                    </div>
                    <div class="tech-name">CSS3</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">
                        📦
                    </div>
                    <div class="tech-name">Git</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recent Works Section -->
    <section id="projects" class="section-padding">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Recent Works</h2>
                <p class="section-subtitle">Some of my latest projects and contributions</p>
            </div>

            <!-- Featured Project -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="project-card">
                        <div class="row g-0">
                            <div class="col-md-6">
                                <div class="project-image" style="height: 300px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                                    🚀 Featured Project
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="project-content" style="height: 300px; display: flex; flex-direction: column; justify-content: center;">
                                    <h3 class="project-title">Full-Stack E-Commerce Platform</h3>
                                    <p class="project-description">
                                        A comprehensive e-commerce solution built with modern technologies. Features include user authentication, payment processing, inventory management, and real-time analytics dashboard.
                                    </p>
                                    <div class="project-tech">
                                        <span class="tech-tag">React</span>
                                        <span class="tech-tag">Node.js</span>
                                        <span class="tech-tag">MongoDB</span>
                                        <span class="tech-tag">Stripe</span>
                                    </div>
                                    <div class="project-links">
                                        <a href="#" class="project-link primary">Live Demo</a>
                                        <a href="#" class="project-link secondary">View Code</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Grid -->
            <div class="project-grid">
                <div class="project-card">
                    <div class="project-image" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                        📱 Mobile App
                    </div>
                    <div class="project-content">
                        <h4 class="project-title">Task Management App</h4>
                        <p class="project-description">
                            A collaborative task management application with real-time updates and team collaboration features.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">React Native</span>
                            <span class="tech-tag">Firebase</span>
                        </div>
                        <div class="project-links">
                            <a href="#" class="project-link primary">View</a>
                            <a href="#" class="project-link secondary">Code</a>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                        🌐 Web App
                    </div>
                    <div class="project-content">
                        <h4 class="project-title">Analytics Dashboard</h4>
                        <p class="project-description">
                            A comprehensive analytics dashboard with real-time data visualization and reporting capabilities.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">Vue.js</span>
                            <span class="tech-tag">D3.js</span>
                            <span class="tech-tag">Python</span>
                        </div>
                        <div class="project-links">
                            <a href="#" class="project-link primary">View</a>
                            <a href="#" class="project-link secondary">Code</a>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                        🤖 AI Tool
                    </div>
                    <div class="project-content">
                        <h4 class="project-title">AI Content Generator</h4>
                        <p class="project-description">
                            An AI-powered content generation tool that helps create engaging content for social media and blogs.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">OpenAI</span>
                            <span class="tech-tag">FastAPI</span>
                        </div>
                        <div class="project-links">
                            <a href="#" class="project-link primary">View</a>
                            <a href="#" class="project-link secondary">Code</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section-padding contact-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Get In Touch</h2>
                <p class="section-subtitle">Ready to start your next project? Let's discuss how we can work together to bring your ideas to life.</p>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="mb-5">
                        <div class="d-flex align-items-center mb-4">
                            <div class="me-4">
                                <div style="width: 60px; height: 60px; background: var(--gradient-primary); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                                    📧
                                </div>
                            </div>
                            <div>
                                <h5 style="color: var(--text-light); margin-bottom: 0.5rem;">Email</h5>
                                <p style="color: var(--text-muted); margin: 0;"><EMAIL></p>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h6 style="color: var(--text-light); margin-bottom: 1rem;">Available for:</h6>
                            <ul style="color: var(--text-muted); list-style: none; padding: 0;">
                                <li style="margin-bottom: 0.5rem;">• Full-stack web development</li>
                                <li style="margin-bottom: 0.5rem;">• API development and integration</li>
                                <li style="margin-bottom: 0.5rem;">• DevOps and deployment solutions</li>
                                <li style="margin-bottom: 0.5rem;">• Technical consulting</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <form class="contact-form">
                        <div class="form-group">
                            <input type="text" class="form-control" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <textarea class="form-control" placeholder="Your Message" required></textarea>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary" style="width: 100%;">Send Message</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 John Doe. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-white me-3"><i class="bi bi-github"></i></a>
                    <a href="#" class="text-white me-3"><i class="bi bi-linkedin"></i></a>
                    <a href="#" class="text-white me-3"><i class="bi bi-twitter"></i></a>
                    <a href="#" class="text-white"><i class="bi bi-envelope"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
        crossorigin="anonymous"></script>
    
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Update active navigation link on scroll
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (scrollY >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>

</html>
