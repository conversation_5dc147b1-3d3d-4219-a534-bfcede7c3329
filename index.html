<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Developer Portfolio - Web Developer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f9fafb;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .hero-section {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .section-padding {
            padding: 5rem 0;
        }
        
        .skill-card {
            transition: transform 0.3s ease;
        }
        
        .skill-card:hover {
            transform: translateY(-5px);
        }
        
        .project-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
        
        .bg-light-custom {
            background-color: var(--bg-light) !important;
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm">
        <div class="container">
            <a class="navbar-brand text-primary" href="#home">Portfolio</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="#skills">Skills</a></li>
                    <li class="nav-item"><a class="nav-link" href="#projects">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section d-flex align-items-center">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">Hi, I'm John Doe</h1>
                    <p class="lead mb-4">A passionate web developer creating modern, responsive websites and applications with clean code and great user experiences.</p>
                    <div class="d-flex gap-3">
                        <a href="#projects" class="btn btn-light btn-lg">View My Work</a>
                        <a href="#contact" class="btn btn-outline-light btn-lg">Get In Touch</a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="bg-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 300px; height: 300px;">
                        <i class="bi bi-code-slash text-primary" style="font-size: 8rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <img src="https://via.placeholder.com/500x400/f8f9fa/6c757d?text=About+Me" class="img-fluid rounded shadow" alt="About Me">
                </div>
                <div class="col-lg-6">
                    <h2 class="mb-4">About Me</h2>
                    <p class="text-muted mb-4">I'm a dedicated web developer with 3+ years of experience in creating digital solutions that make a difference. I specialize in modern web technologies and love turning complex problems into simple, beautiful designs.</p>
                    <p class="text-muted mb-4">When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or enjoying a good cup of coffee while reading about the latest web development trends.</p>
                    <div class="row">
                        <div class="col-6">
                            <h5 class="text-primary">50+</h5>
                            <p class="text-muted">Projects Completed</p>
                        </div>
                        <div class="col-6">
                            <h5 class="text-primary">3+</h5>
                            <p class="text-muted">Years Experience</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="section-padding bg-light-custom">
        <div class="container">
            <div class="text-center mb-5">
                <h2>Skills & Technologies</h2>
                <p class="text-muted">Technologies I work with to bring ideas to life</p>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card skill-card h-100 text-center p-4">
                        <i class="bi bi-code-slash text-primary mb-3" style="font-size: 3rem;"></i>
                        <h5>Frontend Development</h5>
                        <p class="text-muted">HTML5, CSS3, JavaScript, React, Vue.js, Bootstrap</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card skill-card h-100 text-center p-4">
                        <i class="bi bi-server text-primary mb-3" style="font-size: 3rem;"></i>
                        <h5>Backend Development</h5>
                        <p class="text-muted">Node.js, Python, PHP, Express.js, REST APIs</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card skill-card h-100 text-center p-4">
                        <i class="bi bi-database text-primary mb-3" style="font-size: 3rem;"></i>
                        <h5>Database & Tools</h5>
                        <p class="text-muted">MySQL, MongoDB, Git, Docker, AWS</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="section-padding">
        <div class="container">
            <div class="text-center mb-5">
                <h2>Featured Projects</h2>
                <p class="text-muted">Some of my recent work</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card project-card h-100">
                        <img src="https://via.placeholder.com/400x250/6366f1/ffffff?text=E-Commerce+App" class="card-img-top" alt="Project 1">
                        <div class="card-body">
                            <h5 class="card-title">E-Commerce Platform</h5>
                            <p class="card-text text-muted">A full-stack e-commerce solution built with React and Node.js, featuring user authentication, payment integration, and admin dashboard.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">React • Node.js • MongoDB</small>
                                <div>
                                    <a href="#" class="btn btn-sm btn-outline-primary me-2">Live Demo</a>
                                    <a href="#" class="btn btn-sm btn-primary">Code</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card project-card h-100">
                        <img src="https://via.placeholder.com/400x250/10b981/ffffff?text=Task+Manager" class="card-img-top" alt="Project 2">
                        <div class="card-body">
                            <h5 class="card-title">Task Management App</h5>
                            <p class="card-text text-muted">A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">Vue.js • Express • Socket.io</small>
                                <div>
                                    <a href="#" class="btn btn-sm btn-outline-primary me-2">Live Demo</a>
                                    <a href="#" class="btn btn-sm btn-primary">Code</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card project-card h-100">
                        <img src="https://via.placeholder.com/400x250/f59e0b/ffffff?text=Weather+App" class="card-img-top" alt="Project 3">
                        <div class="card-body">
                            <h5 class="card-title">Weather Dashboard</h5>
                            <p class="card-text text-muted">A responsive weather application that provides current conditions and forecasts with beautiful visualizations and location-based services.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">JavaScript • API • Chart.js</small>
                                <div>
                                    <a href="#" class="btn btn-sm btn-outline-primary me-2">Live Demo</a>
                                    <a href="#" class="btn btn-sm btn-primary">Code</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section-padding bg-light-custom">
        <div class="container">
            <div class="text-center mb-5">
                <h2>Get In Touch</h2>
                <p class="text-muted">Let's discuss your next project</p>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="row g-4 mb-5">
                        <div class="col-md-4 text-center">
                            <i class="bi bi-envelope text-primary mb-3" style="font-size: 2rem;"></i>
                            <h6>Email</h6>
                            <p class="text-muted"><EMAIL></p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="bi bi-telephone text-primary mb-3" style="font-size: 2rem;"></i>
                            <h6>Phone</h6>
                            <p class="text-muted">+****************</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="bi bi-geo-alt text-primary mb-3" style="font-size: 2rem;"></i>
                            <h6>Location</h6>
                            <p class="text-muted">New York, NY</p>
                        </div>
                    </div>
                    <form class="bg-white p-4 rounded shadow">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <input type="text" class="form-control" placeholder="Your Name" required>
                            </div>
                            <div class="col-md-6">
                                <input type="email" class="form-control" placeholder="Your Email" required>
                            </div>
                            <div class="col-12">
                                <input type="text" class="form-control" placeholder="Subject" required>
                            </div>
                            <div class="col-12">
                                <textarea class="form-control" rows="5" placeholder="Your Message" required></textarea>
                            </div>
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary btn-lg">Send Message</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 John Doe. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-white me-3"><i class="bi bi-github"></i></a>
                    <a href="#" class="text-white me-3"><i class="bi bi-linkedin"></i></a>
                    <a href="#" class="text-white me-3"><i class="bi bi-twitter"></i></a>
                    <a href="#" class="text-white"><i class="bi bi-envelope"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
        crossorigin="anonymous"></script>
    
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Update active navigation link on scroll
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (scrollY >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>

</html>
